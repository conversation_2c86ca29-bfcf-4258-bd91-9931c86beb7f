import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Form, Modal, App } from 'antd';
import React, { useCallback, useState } from 'react';

// 导入重构后的模块
import type { TaskBasic, TaskBasicSearchParams } from '@/components/SqlMonitor/types';
import { TaskService } from '@/components/SqlMonitor/services';
import { useTaskData, useTaskSelection, useTable, useModal, useDrawer } from '@/components/SqlMonitor/hooks';
import { tableStyles } from '@/components/SqlMonitor/styles';

// 导入拆分的组件
import { createTaskPlanTableColumns } from '@/components/SqlMonitor/components/columns';
import { QuickSearchForm } from '@/components/SqlMonitor/components/forms';
import { TaskPlanActionButtons } from '@/components/SqlMonitor/components/button';
import { TaskPlanTableComponent } from '@/components/SqlMonitor/components/table';
import { ModalManager } from '@/components/SqlMonitor/components/modals';

interface AntdTableProps {
  contentHeight?: number;
}

/**
 * 任务管理表格组件
 * 包含查询、新增、编辑、删除、分页等完整功能
 */
const MainTable: React.FC<AntdTableProps> = ({ contentHeight }) => {
  const { message } = App.useApp();

  // 使用自定义hooks管理状态
  const { data, loading, total, pagination, searchParams, loadData, resetData, updateSearchParams } = useTaskData({ autoLoad: true });

  const { allSelectedRows, rowSelection, clearSelection, getSelectedCount } = useTaskSelection(data, {
    crossPage: true,
    onSelectionChange: (selectedKeys, selectedRows) => {
      console.log('选择变化:', { selectedKeys, selectedRows });
    },
  });

  const { tableScrollY, filteredInfo, handleTableChange, getSortOrder, resetSortAndFilter } = useTable({ contentHeight });

  const { modalState: searchModal, showModal: showSearchModal, hideModal: hideSearchModal } = useModal();

  const { modalState: groupModal, showModal: showGroupModal, hideModal: hideGroupModal } = useModal();

  const { drawerState: editDrawer, showDrawer: showEditDrawer, hideDrawer: hideEditDrawer } = useDrawer();

  // 当前编辑记录
  const [currentRecord, setCurrentRecord] = useState<TaskBasic | null>(null);

  // 表单实例
  const [searchForm] = Form.useForm();

  // 搜索表单提交处理
  const handleSearchFormSubmit = useCallback(
    (values: TaskBasicSearchParams) => {
      console.log('搜索表单数据:', values);
      // 更新搜索参数状态
      updateSearchParams(values);
      // 重置排序和筛选状态
      resetSortAndFilter();
      // 重置选择状态
      clearSelection();
      // 调用参数：搜索表单、默认分页
      loadData(values);
    },
    [updateSearchParams, resetSortAndFilter, clearSelection, loadData]
  );

  // 重置搜索
  const handleReset = useCallback(() => {
    // 重置排序和筛选
    resetSortAndFilter();
    // 重置多选
    clearSelection();
    // 重置搜索参数
    updateSearchParams({});
    // 重置表单
    searchForm.resetFields();
    // 重置数据
    resetData();
  }, [resetSortAndFilter, clearSelection, updateSearchParams, searchForm, resetData]);

  // 详细查询提交
  const handleAdvancedSearch = useCallback(
    async (values: TaskBasicSearchParams) => {
      updateSearchParams(values);

      resetSortAndFilter();
      hideSearchModal();
      clearSelection();

      await loadData(values);
      console.log('详细查询提交:', values);
    },
    [updateSearchParams, loadData, resetSortAndFilter, hideSearchModal, clearSelection]
  );

  // 删除单个任务
  const handleDelete = useCallback(
    async (id: number) => {
      try {
        const res = await TaskService.deleteTask(id);

        if (res.success) {
          message.success(`成功删除任务数量 ${res.total}`);

          // React 状态更新是异步的
          // 计算删除后的新分页参数
          let newPagination = { ...pagination };

          // 判断删除后，分页是否需要调整
          // 如果删除后当前分页的数据为0，就重新请求上一页的数据
          if (Math.ceil((total - res.total) / pagination.page_size) != pagination.current) {
            newPagination = {
              current: pagination.current - 1,
              page_size: pagination.page_size,
            };
          }

          // 删除任务时：分页不变, 搜索参数不变
          const params = {
            ...newPagination,
            ...searchParams,
          };
          await loadData(params);
        }
      } catch (error) {
        message.error('删除失败');
        console.error('删除失败:', error);
      }
    },
    [loadData, message, pagination, searchParams, total]
  );

  // 批量删除任务
  const handleBatchDelete = useCallback(async () => {
    const totalSelected = getSelectedCount();
    if (totalSelected === 0) {
      message.warning('请先选择要删除的任务');
      return;
    }

    const requestDelete = async () => {
      try {
        const ids = Array.from(allSelectedRows.keys()).map(key => Number(key));
        const res = await TaskService.batchDeleteTasks(ids);
        if (res.success) {
          message.success(`成功删除任务数量 ${res.total}`);

          // 清空多选
          clearSelection();

          // React 状态更新是异步的
          // 计算删除后的新分页参数
          let newPagination = { ...pagination };

          // 判断删除后，分页是否需要调整
          // 如果删除后当前分页的数据为0，就重新请求上一页的数据
          if (Math.ceil((total - res.total) / pagination.page_size) != pagination.current) {
            newPagination = {
              current: pagination.current - 1,
              page_size: pagination.page_size,
            };
          }

          // 删除任务时：分页不变, 搜索参数不变
          const params = {
            ...newPagination,
            ...searchParams,
          };
          await loadData(params);
        }
      } catch (error) {
        message.error('批量删除失败');
        console.error('批量删除失败:', error);
      }
    };

    Modal.confirm({
      title: '确认批量删除',
      content: `确定要删除选中的 ${totalSelected} 个任务吗？`,
      icon: <ExclamationCircleOutlined />,
      okText: '确定',
      cancelText: '取消',
      onOk: requestDelete,
    });
  }, [getSelectedCount, allSelectedRows, message, clearSelection, loadData, pagination, searchParams, total]);

  // 抽屉关闭处理
  const handleDrawerClose = useCallback(() => {
    hideEditDrawer();
    setCurrentRecord(null);
  }, [hideEditDrawer]);

  // 编辑任务处理
  const handleEdit = useCallback(
    (record: TaskBasic) => {
      setCurrentRecord(record);
      showEditDrawer({
        title: '编辑任务',
        width: '90%',
      });
    },
    [showEditDrawer]
  );

  // 表格列定义
  const columns = createTaskPlanTableColumns({
    filteredInfo,
    getSortOrder,
    onEdit: handleEdit,
    onDelete: handleDelete,
  });

  return (
    <div className="h-full flex flex-col">
      {/* 主要内容区域  */}
      <div className={tableStyles.mainContainer}>
        {/* 快速搜索表单区域 */}
        <QuickSearchForm
          form={searchForm}
          onSubmit={handleSearchFormSubmit}
          onReset={handleReset}
          onAdvancedSearch={() =>
            showSearchModal({
              title: '详细查询',
              width: 800,
            })
          }
        />

        {/* 操作按钮区域 */}
        <TaskPlanActionButtons
          selectedCount={getSelectedCount()}
          onAddTask={() => {
            setCurrentRecord(null);
            showEditDrawer({
              title: '新增任务',
              width: '90%',
            });
          }}
          onGroupManage={() =>
            showGroupModal({
              title: '分组管理',
              width: 800,
            })
          }
          onBatchDelete={handleBatchDelete}
          onClearSelection={clearSelection}
        />

        {/* 表格主体区域 */}
        <TaskPlanTableComponent
          columns={columns}
          data={data}
          loading={loading}
          total={total}
          pagination={pagination}
          rowSelection={rowSelection}
          tableScrollY={tableScrollY}
          onTableChange={handleTableChange}
          onPaginationChange={(page, pageSize) => {
            // 分页变化时，请求参数为：分页变化 + 搜索参数
            loadData({
              ...searchParams,
              current: page,
              page_size: pageSize,
            });
          }}
        />
      </div>

      {/* 模态框管理 */}
      <ModalManager
        searchModal={searchModal}
        onSearchModalClose={hideSearchModal}
        searchForm={searchForm}
        searchParams={searchParams}
        onAdvancedSearch={handleAdvancedSearch}
        onSearchReset={() => {
          searchForm.resetFields();
          updateSearchParams({});
          hideSearchModal();
        }}
        editDrawer={editDrawer}
        onEditDrawerClose={handleDrawerClose}
        currentRecord={currentRecord}
        onFormSubmit={() => {
          hideEditDrawer();
          setCurrentRecord(null);
          const params = {
            ...pagination,
            ...searchParams,
          };
          loadData(params);
        }}
        onFormCancel={handleDrawerClose}
        onFormReset={() => {
          searchForm.resetFields();
          message.info('表单已重置');
        }}
        groupModal={groupModal}
        onGroupModalClose={hideGroupModal}
      />
    </div>
  );
};

export default MainTable;
