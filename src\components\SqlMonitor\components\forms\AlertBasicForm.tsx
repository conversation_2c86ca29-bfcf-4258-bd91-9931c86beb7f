import React, { useEffect } from 'react';
import { Form, Input, Select, Card, Row, Col, Button } from 'antd';
import type { FormInstance } from 'antd';

import type { TaskAlert } from '../../types';
import { formStyles } from '../../styles';
import { FORM_BUTTON_TEXT } from '../../constants';

const { Option } = Select;
const { TextArea } = Input;

// 表单组件Props类型
interface BasicFormProps<T> {
  form: FormInstance;
  initialData?: T;
  onSubmit?: (values: T) => void;
  onCancel?: () => void;
  onReset?: () => void;
  loading?: boolean;
}

/**
 * 告警基础信息表单组件
 * 包含告警的基本配置信息
 */
export const AlertBasicForm: React.FC<BasicFormProps<TaskAlert>> = ({ form, initialData, onSubmit, onCancel, onReset, loading = false }) => {
  // 初始化表单数据
  useEffect(() => {
    if (initialData) {
      console.log('初始化表单数据:', initialData);

      // 编辑模式：设置表单值
      form.setFieldsValue({
        id: initialData.id,
        name: initialData.name,
        severity: initialData.severity,
        alert_type: initialData.alert_type,
        sql: initialData.sql,
        values: initialData.values,
      });
    } else {
      // 新增模式：重置表单
      form.resetFields();
    }
  }, [initialData, form]);

  // 表单提交处理
  const handleSubmit = async () => {
    try {
      // 验证表单
      const formValues = await form.validateFields();
      // 处理触发值
      const processedValues = {
        ...formValues,
        // 如果value为空，置为0
        values: formValues.values || '0',
      };

      // 提交表单
      onSubmit?.(processedValues);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 重置表单
  const handleReset = () => {
    form.resetFields();
    onReset?.();
  };

  // 取消表单
  const handleCancel = () => {
    // 重置表单
    form.resetFields();
    // 调用父组件的取消回调
    onCancel?.();
  };

  return (
    <div className="h-full flex flex-col">
      <Form form={form} layout="vertical" className="flex-1 overflow-auto">
        {/* 隐藏的 id 字段，用于编辑时保持 id */}
        <Form.Item name="id" hidden>
          <Input />
        </Form.Item>
        <div className={formStyles.tabContent}>
          <Card title="告警基本信息" size="small" className="mb-4">
            <Row gutter={16} className="mb-4">
              <Col span={12}>
                <Form.Item label="告警名称" name="name" rules={[{ required: true, message: '请输入告警名称' }]}>
                  <Input placeholder="请输入告警名称" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="告警级别" name="severity" rules={[{ required: true, message: '请选择告警级别' }]}>
                  <Input placeholder="请选择告警级别" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16} className="mb-4">
              <Col span={24}>
                <Form.Item label="告警类型" name="alert_type" rules={[{ required: true, message: '请选择告警类型' }]}>
                  <Select placeholder="请选择告警类型">
                    <Option value="isExist">存在性判断</Option>
                    <Option value="isValue">查询值判断</Option>
                    <Option value="isChange">查询值变化判断</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16} className="mb-4">
              <Col span={24}>
                <Form.Item label="SQL语句" name="sql" rules={[{ required: true, message: '请输入SQL语句' }]}>
                  <TextArea
                    placeholder="请输入SQL语句，支持多行输入"
                    rows={4}
                    showCount
                    // maxLength={1000}
                    autoSize={{ minRows: 4, maxRows: 10 }}
                    style={{ resize: 'vertical' }}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16} className="mb-4">
              <Col span={24}>
                <Form.Item
                  label="触发值"
                  name="values"
                  help={
                    <div className="py-2">
                      <div>查询值判断: 多个值用逗号分隔，如：=0,&gt;=1,{'<'}2</div>
                      <div>查询值变化判断: 时间间隔(秒) 60s</div>
                      <div>存在性判断: ''</div>
                    </div>
                  }
                >
                  <Input placeholder="请输入触发值，多个值用逗号分隔" />
                </Form.Item>
              </Col>
            </Row>
          </Card>
        </div>
      </Form>

      {/* 底部操作栏 */}
      <div className={formStyles.footerContainer}>
        <div className="flex justify-between items-center">
          <div className={formStyles.footerHint}>{initialData ? '编辑告警信息' : '创建新告警'}</div>
          <div className={formStyles.buttonGroup}>
            <Button onClick={handleCancel} className={`${formStyles.actionButton} ${formStyles.cancelButton}`}>
              {FORM_BUTTON_TEXT.cancel}
            </Button>
            {/* 新增模式下显示重置按钮 */}
            {!initialData && (
              <Button onClick={handleReset} className={`${formStyles.actionButton} ${formStyles.resetButton}`}>
                {FORM_BUTTON_TEXT.reset}
              </Button>
            )}
            <Button type="primary" loading={loading} onClick={handleSubmit} className={`${formStyles.actionButton} ${initialData ? formStyles.confirmButton : formStyles.submitButton}`}>
              {initialData ? FORM_BUTTON_TEXT.update : FORM_BUTTON_TEXT.submit}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

