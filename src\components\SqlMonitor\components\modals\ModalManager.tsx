import { EditOutlined, FilterOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, <PERSON><PERSON>, But<PERSON> } from 'antd';
import React from 'react';

import type { TaskBasic, TaskBasicSearchParams } from '../../types';
import { AdvancedSearchForm } from '../forms/SearchForm';
import ComplexTaskForm from '../forms/ComplexTaskForm';
import GroupManagementModal from './GroupManagementModal';
import { FORM_BUTTON_TEXT } from '@/components/SqlMonitor/constants';
import { formStyles } from '@/components/SqlMonitor/styles';

interface ModalManagerProps {
  // 搜索模态框
  searchModal: {
    visible: boolean;
    width?: string | number;
  };
  onSearchModalClose: () => void;
  searchForm: any;
  searchParams: TaskBasicSearchParams;
  onAdvancedSearch: (values: TaskBasicSearchParams) => void;
  onSearchReset: () => void;

  // 编辑抽屉
  editDrawer: {
    visible: boolean;
    width?: string | number;
  };
  onEditDrawerClose: () => void;
  currentRecord: TaskBasic | null;
  onFormSubmit: () => void;
  onFormCancel: () => void;
  onFormReset: () => void;

  // 表单状态
  isEditMode?: boolean;
  submitLoading?: boolean;

  // 分组管理模态框
  groupModal: {
    visible: boolean;
  };
  onGroupModalClose: () => void;
}

/**
 * 模态框管理组件
 * 统一管理所有模态框和抽屉
 */
export const ModalManager: React.FC<ModalManagerProps> = ({
  searchModal,
  onSearchModalClose,
  searchForm,
  searchParams,
  onAdvancedSearch,
  onSearchReset,
  editDrawer,
  onEditDrawerClose,
  currentRecord,
  onFormSubmit,
  onFormCancel,
  onFormReset,
  isEditMode = false,
  submitLoading = false,
  groupModal,
  onGroupModalClose,
}) => {
  return (
    <>
      {/* 详细查询Modal */}
      <Modal
        title={
          <div className="flex items-center gap-2">
            <FilterOutlined className="text-blue-600" />
            <span className="text-lg font-semibold">详细查询</span>
          </div>
        }
        open={searchModal.visible}
        onCancel={onSearchModalClose}
        footer={null}
        width={searchModal.width}
        className="custom-modal"
      >
        <AdvancedSearchForm form={searchForm} onSubmit={onAdvancedSearch} onReset={onSearchReset} searchParams={searchParams} />
      </Modal>

      {/* 编辑抽屉 */}
      <Drawer
        title={
          <div className="flex items-center gap-2">
            <EditOutlined className="text-blue-600" />
            <span className="text-lg font-semibold">{currentRecord ? '编辑任务' : '新增任务'}</span>
          </div>
        }
        width={editDrawer.width}
        open={editDrawer.visible}
        onClose={onEditDrawerClose}
        maskClosable={false}
        className="custom-drawer"
        footer={
          <div className={formStyles.footerContainer}>
            <div className="flex justify-between items-center">
              <div className={formStyles.footerHint}>{isEditMode ? '编辑任务信息' : '创建新任务'}</div>
              <div className={formStyles.buttonGroup}>
                {/* 取消按钮 */}
                <Button onClick={onFormCancel} className={`${formStyles.actionButton} ${formStyles.cancelButton}`}>
                  {FORM_BUTTON_TEXT.cancel}
                </Button>
                {/* 新增模式下显示重置按钮 */}
                {!isEditMode && (
                  <Button onClick={onFormReset} className={`${formStyles.actionButton} ${formStyles.resetButton}`}>
                    {FORM_BUTTON_TEXT.reset}
                  </Button>
                )}
                {/* 提交按钮 */}
                <Button type="primary" loading={submitLoading} onClick={onFormSubmit} className={`${formStyles.actionButton} ${isEditMode ? formStyles.confirmButton : formStyles.submitButton}`}>
                  {isEditMode ? FORM_BUTTON_TEXT.update : FORM_BUTTON_TEXT.submit}
                </Button>
              </div>
            </div>
          </div>
        }
      >
        <ComplexTaskForm initialData={currentRecord || undefined} hideFooter />
      </Drawer>

      {/* 分组管理Modal */}
      <GroupManagementModal visible={groupModal.visible} onCancel={onGroupModalClose} />
    </>
  );
};
